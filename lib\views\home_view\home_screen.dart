import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:readmore/readmore.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/home_model/detailed_profile_model.dart';
import 'package:room_eight/models/home_model/get_all_profile_model.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';
import 'package:room_eight/viewmodels/home_bloc/home_bloc.dart';
import 'package:room_eight/views/home_view/widgets/user_detail_shimmer.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:swipable_stack/swipable_stack.dart';

class HomeScreen extends StatelessWidget {
  static Widget builder(BuildContext context) => const HomeScreen();

  const HomeScreen({super.key});

  void _onSwipeCompleted(
    BuildContext context,
    int index,
    SwipeDirection direction,
    HomeState state,
  ) {
    if (direction == SwipeDirection.right) {
      // Accept logic
      context.read<HomeBloc>().add(AcceptUser(state.users[index].id ?? 0));
    } else if (direction == SwipeDirection.left) {
      // Reject logic
      context.read<HomeBloc>().add(RejectUser(state.users[index].id ?? 0));
    }
    // The SwipableStack will automatically show the next card
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HomeBloc, HomeState>(
      listenWhen: (previous, current) =>
          previous.hasSeenTutorial != current.hasSeenTutorial,
      listener: (context, state) {},
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.onPrimary,
        body: Stack(
          children: [
            BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                if (state.users.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomImageView(
                          imagePath: Assets.images.svgs.icons.icUser.path,
                          height: 80.h,
                          width: 80.w,
                          color: Theme.of(
                            context,
                          ).customColors.primaryColor!.withValues(alpha: 0.5),
                        ),
                        buildSizedBoxH(24.h),
                        Text(
                          Lang.of(context).lbl_no_more_users,
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(
                                  context,
                                ).customColors.blackColor,
                                fontSize: 20.sp,
                              ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                return Stack(
                  children: [
                    // Swipable content
                    // SwipableStack(
                    //   controller: state.swipableController,
                    //   itemCount: state.users.length,
                    //   onSwipeCompleted: (index, direction) =>
                    //       _onSwipeCompleted(context, index, direction, state),
                    //   builder: (context, properties) {
                    //     final user = state.users[properties.index];
                    //     context.read().add(user.id);
                    //     return _buildUserPage(context, user);
                    //   },
                    //   detectableSwipeDirections: const {
                    //     SwipeDirection.left,
                    //     SwipeDirection.right,
                    //   },
                    //   stackClipBehaviour: Clip.none,
                    //   overlayBuilder: (context, properties) {
                    //     // if (properties.direction == null)
                    //     //   return const SizedBox.shrink();
                    //     final progress = properties.swipeProgress.clamp(
                    //       0.0,
                    //       1.0,
                    //     );
                    //     if (properties.direction == SwipeDirection.right) {
                    //       // Accept (green)
                    //       return Align(
                    //         alignment: Alignment.centerLeft,
                    //         child: Opacity(
                    //           opacity: progress,
                    //           child: Container(
                    //             margin: EdgeInsets.all(32),
                    //             padding: EdgeInsets.symmetric(
                    //               horizontal: 24,
                    //               vertical: 12,
                    //             ),
                    //             decoration: BoxDecoration(
                    //               color: Theme.of(context)
                    //                   .customColors
                    //                   .greencolor!
                    //                   .withValues(alpha: 0.5),
                    //               borderRadius: BorderRadius.circular(16),
                    //             ),
                    //             child: Text(
                    //               Lang.of(context).lbl_accept,
                    //               style: Theme.of(context).textTheme.bodyLarge!
                    //                   .copyWith(
                    //                     color: Theme.of(
                    //                       context,
                    //                     ).customColors.fillColor!,
                    //                     fontSize: 32,
                    //                     fontWeight: FontWeight.bold,
                    //                   ),
                    //             ),
                    //           ),
                    //         ),
                    //       );
                    //     } else if (properties.direction ==
                    //         SwipeDirection.left) {
                    //       // Reject (red)
                    //       return Align(
                    //         alignment: Alignment.centerRight,
                    //         child: Opacity(
                    //           opacity: progress,
                    //           child: Container(
                    //             margin: EdgeInsets.all(32),
                    //             padding: EdgeInsets.symmetric(
                    //               horizontal: 24,
                    //               vertical: 12,
                    //             ),
                    //             decoration: BoxDecoration(
                    //               color: Theme.of(context)
                    //                   .customColors
                    //                   .redcolor!
                    //                   .withValues(alpha: 0.5),
                    //               borderRadius: BorderRadius.circular(16),
                    //             ),
                    //             child: Text(
                    //               Lang.of(context).lbl_reject,
                    //               style: Theme.of(context).textTheme.bodyLarge!
                    //                   .copyWith(
                    //                     color: Theme.of(
                    //                       context,
                    //                     ).customColors.fillColor!,
                    //                     fontSize: 32,
                    //                     fontWeight: FontWeight.bold,
                    //                   ),
                    //             ),
                    //           ),
                    //         ),
                    //       );
                    //     }
                    //     return const SizedBox.shrink();
                    //   },
                    // ),
                    // Only show CardSwiper if there are users available
                    state.users.isEmpty
                        ? Center(
                            child: state.loadHomePageData
                                ? Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      CircularProgressIndicator(),
                                      SizedBox(height: 16),
                                      Text(
                                        'Loading profiles...',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium
                                            ?.copyWith(color: Colors.grey[600]),
                                      ),
                                    ],
                                  )
                                : Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.people_outline,
                                        size: 64,
                                        color: Colors.grey[400],
                                      ),
                                      SizedBox(height: 16),
                                      Text(
                                        'No profiles available',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium
                                            ?.copyWith(color: Colors.grey[600]),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'Check back later for new profiles',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(color: Colors.grey[500]),
                                      ),
                                    ],
                                  ),
                          )
                        : CardSwiper(
                            cardsCount: state.users.length,
                            numberOfCardsDisplayed: 1, // Ensure 3 are visible
                            backCardOffset: const Offset(
                              0,
                              40,
                            ), // Controls how much the back cards peek
                            padding: EdgeInsets.symmetric(
                              horizontal: 0,
                              vertical: 0,
                            ), // Matches card spacing
                            scale: 0.95, // Creates the stacked feel
                            isLoop: true,
                            allowedSwipeDirection: AllowedSwipeDirection.only(
                              left: true,
                              right: true,
                            ),
                            onSwipe: (previousIndex, currentIndex, direction) {
                              // Add safety checks for array bounds
                              if (previousIndex >= state.users.length) {
                                Logger.lOG(
                                  "Invalid previousIndex: $previousIndex, users length: ${state.users.length}",
                                );
                                return false;
                              }

                              final user = state.users[previousIndex];
                              if (direction == CardSwiperDirection.left) {
                                context.read<HomeBloc>().add(
                                  RejectUser(user.id ?? 0),
                                );
                              } else if (direction ==
                                  CardSwiperDirection.right) {
                                context.read<HomeBloc>().add(
                                  AcceptUser(user.id ?? 0),
                                );
                              }

                              // Add safety check for currentIndex
                              final safeCurrentIndex = currentIndex ?? 0;
                              if (safeCurrentIndex < state.users.length) {
                                context.read<HomeBloc>().add(
                                  CurrentProfileIdChange(
                                    state.users[safeCurrentIndex].id ?? 0,
                                  ),
                                );
                              }

                              return true;
                            },
                            onEnd: () {
                              Logger.lOG("All cards swiped");
                            },
                            cardBuilder: (context, index, percentX, percentY) {
                              // Add safety check for index bounds
                              if (index >= state.users.length) {
                                Logger.lOG(
                                  "Invalid index: $index, users length: ${state.users.length}",
                                );
                                return const SizedBox.shrink();
                              }

                              final user = state.users[index];
                              return ClipRRect(
                                child: Container(
                                  child: _buildUserPage(
                                    context,
                                    user,
                                  ), // your card UI
                                ),
                              );
                            },
                          ),
                    // Fixed top section overlay
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: _buildTopSection(context, state),
                    ),
                  ],
                );
              },
            ),
            BlocBuilder<HomeBloc, HomeState>(
              builder: (context, state) {
                if (!state.hasSeenTutorial) {
                  return _buildTutorialOverlay(context);
                }
                return const SizedBox.shrink();
              },
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildBackgroundWithGradient(BuildContext context, HomeState state) {
  //   return PageView.builder(
  //     controller: state.pageController,
  //     scrollDirection: Axis.vertical,
  //     itemCount: state.users.length,
  //     onPageChanged: (index) {
  //       context.read<HomeBloc>().add(HomePageChanged(index));
  //     },
  //     itemBuilder: (context, index) {
  //       return _buildUserPage(context, state.users[index]);
  //     },
  //   );
  // }

  Widget _buildUserPage(BuildContext context, UserData user) {
    context.read<HomeBloc>().add(CurrentProfileIdChange(user.id ?? 0));
    return BackgroundImage(
      imagePath: ApiEndPoint.getImageUrl + (user.profileImage ?? ''),
      showGradient: true,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Remove the top section from here since it's now fixed
          SizedBox(
            height: 112.h,
          ), // Add spacing to account for fixed top section
          _buildUserInfoSection(context, user),
        ],
      ),
    );
  }

  Widget _buildTopSection(BuildContext context, HomeState state) {
    return Container(
      // Add a semi-transparent background to ensure visibility
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.black.withValues(alpha: 0.3), Colors.transparent],
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(
          left: 16.w,
          right: 16.w,
          top: 60.h,
          bottom: 16.h,
        ),
        child: _buildTopBar(context, state),
      ),
    );
  }

  Widget _buildTopBar(BuildContext context, HomeState state) {
    final customColors = Theme.of(context).customColors;
    final gradientColors = _getGradientColors(customColors);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildTopBarButton(
          context,
          iconPath: Assets.images.svgs.icons.icSecurity.path,
          iconColor: customColors.fillColor,
          gradientColors: gradientColors,
        ),
        GestureDetector(
          onTap: () {
            Logger.lOG("hello");
            _buildSettingBottomSheet(context, state);
          },
          child: _buildTopBarButton(
            context,
            iconPath: Assets.images.svgs.icons.icSetting.path,
            gradientColors: gradientColors,
          ),
        ),
      ],
    );
  }

  Widget _buildTopBarButton(
    BuildContext context, {
    required String iconPath,
    required Map<String, Color> gradientColors,
    Color? iconColor,
  }) {
    return CustomGradientContainer(
      height: 36.w,
      width: 36.w,
      topColor: gradientColors['topColor']!,
      bottomColor: gradientColors['bottomColor']!,
      fillColor: gradientColors['fillColor']!,
      child: CustomImageView(
        imagePath: iconPath,
        color: iconColor,
        margin: const EdgeInsets.all(1.5),
      ),
    );
  }

  Widget _buildUserInfoSection(BuildContext context, UserData user) {
    return Padding(
      padding: EdgeInsets.only(bottom: 90.h, right: 16.w, left: 16.w),
      child: _buildUserInfo(context, user),
    );
  }

  Widget _buildUserInfo(BuildContext context, UserData user) {
    final customColors = Theme.of(context).customColors;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // if (user.isNewHere) ...[
        //   _buildNewHereTag(context, customColors),
        //   buildSizedBoxH(12.h),
        // ],
        _buildUserProfile(context, customColors, user),
        buildSizedBoxH(12.h),
        // _buildUserTags(context, customColors, user.tags),
        buildSizedBoxH(12.h),
        // _buildUserDescription(context, customColors, user.),
      ],
    );
  }

  // Widget _buildNewHereTag(BuildContext context, dynamic customColors) {
  //   final gradientColors = _getGradientColors(customColors, alphaValue: 100);

  //   return CustomGradientContainer(
  //     enableBlur: true,
  //     height: 36.h,
  //     padding: const EdgeInsets.symmetric(horizontal: 16.0),
  //     topColor: gradientColors['topColor']!,
  //     bottomColor: gradientColors['bottomColor']!,
  //     fillColor: gradientColors['fillColor']!,
  //     child: Text(
  //       Lang.of(context).lbl_new_here,
  //       textAlign: TextAlign.center,
  //       style: Theme.of(context).textTheme.bodyMedium!.copyWith(
  //         color: customColors.fillColor,
  //         fontWeight: FontWeight.w600,
  //       ),
  //     ),
  //   );
  // }

  Widget _buildUserProfile(
    BuildContext context,
    dynamic customColors,
    UserData user,
  ) {
    return Row(
      children: [
        CustomImageView(
          imagePath: ApiEndPoint.getImageUrl + (user.profileImage ?? ''),
          height: 50.h,
          width: 50.w,
          fit: BoxFit.cover,
          radius: BorderRadius.circular(100.r),
          onTap: () {
            context.read<HomeBloc>().add(GetUserProfileByID(user.id ?? 0));
            _showUserDetailSheet(context, user);
          },
        ),
        buildSizedboxW(12.w),
        GestureDetector(
          onTap: () {
            context.read<HomeBloc>().add(GetUserProfileByID(user.id ?? 0));
            _showUserDetailSheet(context, user);
          },
          child: Text(
            user.name ?? '',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              color: customColors.fillColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  void _showUserDetailSheet(BuildContext context, UserData user) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      useRootNavigator: true,
      builder: (context) =>
          CustomBottomSheetWidget(child: _UserDetailContent()),
    );
  }

  // Widget _buildUserTags(
  //   BuildContext context,
  //   dynamic customColors,
  //   List<String> userTags,
  // ) {
  //   return Wrap(
  //     spacing: 8.w,
  //     runSpacing: 8.h,
  //     // alignment: WrapAlignment.center,
  //     children: userTags
  //         .map((tag) => _buildUserTag(context, tag, customColors))
  //         .toList(),
  //   );
  // }

  // Widget _buildUserTag(BuildContext context, String tag, dynamic customColors) {
  //   return CustomGradientContainer(
  //     padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
  //     topColor: customColors.lightGreyTextColor!,
  //     bottomColor: customColors.lightGreyTextColor!,
  //     fillColor: Colors.transparent,
  //     height: 32.h,
  //     child: Text(
  //       tag,
  //       style: Theme.of(context).textTheme.bodySmall!.copyWith(
  //         color: customColors.lightGreyTextColor,
  //         fontWeight: FontWeight.w500,
  //       ),
  //     ),
  //   );
  // }

  // Widget _buildUserDescription(
  //   BuildContext context,
  //   dynamic customColors,
  //   String description,
  // ) {
  //   return Text(
  //     description,
  //     style: Theme.of(context).textTheme.bodySmall!.copyWith(
  //       color: customColors.lightGreyTextColor,
  //       fontWeight: FontWeight.w500,
  //     ),
  //   );
  // }

  Map<String, Color> _getGradientColors(
    dynamic customColors, {
    int alphaValue = 75,
  }) {
    return {
      'topColor': customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
      'bottomColor': customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
      'fillColor': customColors.fillColor!.withAlpha(alphaValue),
    };
  }

  Widget _buildTutorialOverlay(BuildContext context) {
    final customColors = Theme.of(context).customColors;
    return Positioned.fill(
      child: GestureDetector(
        onTap: () => context.read<HomeBloc>().add(const DismissTutorial()),
        child: Container(
          color: Colors.black.withValues(alpha: 0.85),
          child: Stack(
            children: [
              // Animated swipe indicators
              Positioned(
                left: 60.w,
                bottom: MediaQuery.of(context).size.height * 0.15,
                child: _buildSwipeIndicator(
                  context,
                  Lang.of(context).lbl_reject.toUpperCase(),
                  customColors.redcolor ?? Colors.red,
                  Icons.close_rounded,
                  isLeft: true,
                ),
              ),
              Positioned(
                right: 60.w,
                bottom: MediaQuery.of(context).size.height * 0.15,
                child: _buildSwipeIndicator(
                  context,
                  Lang.of(context).lbl_accept.toUpperCase(),
                  customColors.greencolor ?? Colors.green,
                  Icons.favorite_rounded,
                  isLeft: false,
                ),
              ),
              // Center content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Main instruction text
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 32.w,
                        vertical: 24.h,
                      ),
                      margin: EdgeInsets.symmetric(horizontal: 40.w),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.touch_app_rounded,
                            color: customColors.primaryColor,
                            size: 48.w,
                          ),
                          buildSizedBoxH(16.h),
                          Text(
                            Lang.of(context).lbl_swipe_to_connect,
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 24.sp,
                                ),
                            textAlign: TextAlign.center,
                          ),
                          buildSizedBoxH(8.h),
                          Text(
                            Lang.of(context).lbl_discover_people,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16.sp,
                                ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    buildSizedBoxH(40.h),
                    // Swipe direction indicators
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildDirectionGuide(
                          context,
                          Lang.of(context).lbl_swipe_left,
                          Lang.of(context).lbl_to_reject,
                          Icons.arrow_back_ios_rounded,
                          customColors.redcolor ?? Colors.red,
                        ),
                        Container(
                          width: 2.w,
                          height: 60.h,
                          color: Colors.white.withValues(alpha: 0.3),
                        ),
                        _buildDirectionGuide(
                          context,
                          Lang.of(context).lbl_swipe_right,
                          Lang.of(context).lbl_to_accept,
                          Icons.arrow_forward_ios_rounded,
                          customColors.greencolor ?? Colors.green,
                        ),
                      ],
                    ),
                    buildSizedBoxH(60.h),
                    // Tap anywhere hint
                    // Container(
                    //   padding: EdgeInsets.symmetric(
                    //     horizontal: 16.w,
                    //     vertical: 8.h,
                    //   ),
                    //   decoration: BoxDecoration(
                    //     color: Colors.white.withValues(alpha:0.1),
                    //     borderRadius: BorderRadius.circular(20.r),
                    //     border: Border.all(
                    //       color: Colors.white.withValues(alpha:0.2),
                    //       width: 1,
                    //     ),
                    //   ),
                    //   child: Text(
                    //     'Tap anywhere to continue',
                    //     style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    //       color: Colors.white.withValues(alpha:0.7),
                    //       fontWeight: FontWeight.w500,
                    //       fontSize: 14.sp,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
              // Animated pulse effect
              BlocBuilder<HomeBloc, HomeState>(
                builder: (context, state) {
                  if (state.isPulseAnimating) {
                    return Positioned.fill(
                      child: TweenAnimationBuilder<double>(
                        tween: Tween(begin: 0.0, end: 1.0),
                        duration: const Duration(seconds: 3),
                        builder: (context, value, child) {
                          return Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: customColors.primaryColor!.withValues(
                                  alpha: (1.0 - value) * 0.3,
                                ),
                                width: 2,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSwipeIndicator(
    BuildContext context,
    String text,
    Color color,
    IconData icon, {
    required bool isLeft,
  }) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(seconds: 2),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(isLeft ? -20 * (1 - value) : 20 * (1 - value), 0),
          child: Opacity(
            opacity: 0.3 + (value * 0.7),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: color.withValues(alpha: 0.6),
                      width: 2,
                    ),
                  ),
                  child: Icon(icon, color: color, size: 28.w),
                ),
                buildSizedBoxH(8.h),
                Text(
                  text,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDirectionGuide(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32.w),
        buildSizedBoxH(8.h),
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16.sp,
          ),
        ),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
            fontWeight: FontWeight.w500,
            fontSize: 14.sp,
          ),
        ),
      ],
    );
  }

  void _buildSettingBottomSheet(BuildContext context, HomeState state) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      useRootNavigator: true,
      builder: (context) => CustomBottomSheetWidget(
        child: SizedBox(
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                CustomElevatedButton(
                  text: "Report Profile",
                  buttonTextStyle: Theme.of(context).textTheme.bodySmall!
                      .copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 16.sp,
                        color: Theme.of(context).customColors.fillColor,
                      ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    // Add break connection logic here
                    _buildReportProfileBouttomSheet(context, state);
                  },
                ),
                buildSizedBoxH(20.h),
                CustomElevatedButton(
                  text: "Block Profile",
                  buttonTextStyle: Theme.of(context).textTheme.bodySmall!
                      .copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 16.sp,
                        color: Theme.of(context).customColors.fillColor,
                      ),
                  onPressed: () {
                    Navigator.of(context).pop();
                    showDialog(
                      context: context,
                      builder: (context) => CustomAlertDialog(
                        title: 'Block Profile',
                        subtitle: 'Are you sure you want to Block this user?',
                        confirmButtonText: 'ok',
                        cancelButtonText: 'Cancel',
                        isLoading: false,
                        onConfirmButtonPressed: () {
                          Logger.lOG(state.currentProfileId);
                          context.read<HomeBloc>().add(
                            BlockProfileSubmit(state.currentProfileId),
                          );
                          Navigator.of(context).pop();
                        },
                        onCancelButtonPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _buildReportProfileBouttomSheet(BuildContext context, HomeState state) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      useRootNavigator: true,
      builder: (context) => CustomBottomSheetWidget(
        child: Padding(
          // Padding to adjust for keyboard
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "  Enter Resone",
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    fontSize: 16.sp,
                    color: Theme.of(context).customColors.blackColor,
                  ),
                ),
                CustomTextInputField(
                  context: context,
                  type: InputType.text,
                  hintLabel: "Enter Reason",
                  controller: state.reportController,
                ),
                buildSizedBoxH(20.h),
                CustomElevatedButton(
                  text: "Submit",
                  buttonTextStyle: Theme.of(context).textTheme.bodySmall!
                      .copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 16.sp,
                        color: Theme.of(context).customColors.fillColor,
                      ),
                  onPressed: () {
                    if (state.reportController.text.isNotEmpty) {
                      Navigator.of(context).pop();
                      showDialog(
                        context: context,
                        builder: (context) => CustomAlertDialog(
                          title: 'Report Profile',
                          subtitle:
                              'Are you sure you want to Report  this user?',
                          confirmButtonText: 'ok',
                          cancelButtonText: 'Cancel',
                          isLoading: false,
                          onConfirmButtonPressed: () {
                            context.read<HomeBloc>().add(
                              ReportProfileSubmit(state.currentProfileId),
                            );
                            Navigator.of(context).pop();
                          },
                          onCancelButtonPressed: () {
                            Navigator.of(context).pop();
                          },
                        ),
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _UserDetailContent extends StatelessWidget {
  const _UserDetailContent();

  @override
  Widget build(BuildContext context) {
    final customColors = Theme.of(context).customColors;

    return SingleChildScrollView(
      child: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          final user = state.userProfileDetail;

          if (state.isloadUserDetail) {
            return UserDetailShimmer();
          }

          if (user == null) {
            return const SizedBox(); // or handle gracefully
          }
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildUserBasicInfo(context, customColors, user),
              buildSizedBoxH(16.h),
              _buildOptionsList(
                context: context,
                options: state.userOptionsData,
              ),
              buildSizedBoxH(16.h),
              _buildActionButtons(context),
              buildSizedBoxH(16.h),
            ],
          );
        },
      ),
    );
  }

  Widget _buildUserBasicInfo(
    BuildContext context,
    dynamic customColors,
    ProfileData user,
  ) {
    // final List<String> allImages = [user.imagePath, ...user.otherPhotos];
    final List<String> allImages = user.additionalProfilesImages ?? [];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          if (allImages.isNotEmpty)
            Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadiusGeometry.circular(16),
                    child: CarouselSlider(
                      options: CarouselOptions(
                        height: 210.h,
                        aspectRatio: 1,
                        viewportFraction: 1,
                        enableInfiniteScroll: allImages.length > 1,
                        enlargeCenterPage: true,
                        autoPlay: false,
                        onPageChanged: (index, reason) {
                          context.read<HomeBloc>().add(
                            CarouselPageChanged(index),
                          );
                        },
                      ),
                      items: allImages
                          .map(
                            (img) => CustomImageView(
                              imagePath: ApiEndPoint.getImageUrl + img,
                              height: 210.h,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              radius: BorderRadius.circular(16.r),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
                buildSizedBoxH(8.h),
                BlocBuilder<HomeBloc, HomeState>(
                  builder: (context, state) {
                    return AnimatedSmoothIndicator(
                      activeIndex: state.carouselCurrentIndex,
                      count: allImages.length,
                      effect: CustomizableEffect(
                        activeDotDecoration: DotDecoration(
                          width: 18.w,
                          height: 6.h,
                          color: customColors.primaryColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        dotDecoration: DotDecoration(
                          width: 6.w,
                          height: 6.h,
                          color: customColors.lightGreyTextColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        spacing: 3.0,
                      ),
                    );
                  },
                ),
              ],
            ),
          buildSizedBoxH(16.h),
          Text(
            // user.name,
            user.name ?? '',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: customColors.blackColor,
              fontWeight: FontWeight.bold,
              fontSize: 20.sp,
            ),
          ),
          buildSizedBoxH(4.h),
          Text(
            // user.role,
            "user role",
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: customColors.darkGreytextcolor,
              fontWeight: FontWeight.w500,
              fontSize: 16.sp,
            ),
          ),
          buildSizedBoxH(16.h),
          ReadMoreText(
            user.about ?? '',
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
              color: customColors.darkGreytextcolor,
              fontWeight: FontWeight.w500,
              fontSize: 14.sp,
            ),
            trimMode: TrimMode.Line,
            trimLines: 3,
            trimCollapsedText: 'Show more',
            trimExpandedText: 'Show less',
            moreStyle: TextStyle(
              color: Theme.of(context).customColors.primaryColor,
            ),
            lessStyle: TextStyle(
              color: Theme.of(context).customColors.primaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOptionsList({
    required BuildContext context,
    required List<ProfileOptionModel> options,
  }) {
    if (options.isEmpty) {
      return const Text("No options available");
    }
    Logger.lOG("_buildOptionsList ${options.length}");
    return Wrap(
      children: List.generate(
        options.length,
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: _buildOptionDesign(
            context,
            options[index].name ?? '',
            options[index].icon ?? '',
          ),
        ),
      ),
    );
  }

  Widget _buildOptionDesign(BuildContext context, String text, String image) {
    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomImageView(
            imagePath: ApiEndPoint.getImageUrl + image,
            height: 20.h,
            width: 20.w,
          ),
          buildSizedboxW(5.w),
          Text(
            text,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).customColors.darkGreytextcolor,
              fontSize: 16.0.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      backgroundColor: Theme.of(context).customColors.fillColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(30.r), // Change radius as needed
        side: BorderSide(color: Colors.transparent, width: 0), // No border
      ),
    );
  }

  // Widget _buildTagChip(BuildContext context, String tag, dynamic customColors) {
  //   return CustomGradientContainer(
  //     padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
  //     topColor: customColors.darkGreytextcolor!,
  //     bottomColor: customColors.darkGreytextcolor!,
  //     fillColor: Colors.transparent,
  //     height: 32.h,
  //     child: Text(
  //       tag,
  //       style: Theme.of(context).textTheme.bodySmall!.copyWith(
  //         color: customColors.darkGreytextcolor,
  //         fontWeight: FontWeight.w500,
  //       ),
  //     ),
  //   );
  // }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: CustomElevatedButton(
              text: Lang.of(context).lbl_break_connection,
              buttonTextStyle: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.w700,
                color: Theme.of(context).customColors.fillColor,
              ),
              onPressed: () {
                Navigator.of(context).pop();
                // Add break connection logic here
              },
            ),
          ),
          buildSizedboxW(12.w),
          Expanded(
            child: CustomElevatedButton(
              text: Lang.of(context).lbl_continue_with_chat,
              buttonTextStyle: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.w700,
                color: Theme.of(context).customColors.fillColor,
              ),
              onPressed: () {
                Navigator.of(context).pop();
                // Add continue with chat logic here
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Full Screen Shimmer Widget - Complete card shimmer
Widget buildFullScreenShimmer(BuildContext context) {
  return Shimmer.fromColors(
    baseColor: Colors.grey[300]!,
    highlightColor: Colors.grey[100]!,
    child: Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // Background shimmer
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(16),
            ),
          ),

          // Gradient overlay to match your design
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.3),
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
          ),

          // User profile shimmer positioned at bottom
          Positioned(
            bottom: 90,
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User profile row shimmer
                Row(
                  children: [
                    // Profile image shimmer
                    Container(
                      height: 50,
                      width: 50,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Name shimmer
                    Container(
                      height: 20,
                      width: 120,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Additional content shimmer lines (optional)
                Container(
                  height: 14,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 14,
                  width: MediaQuery.of(context).size.width * 0.6,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),

          // Top section shimmer (for top bar area)
          Positioned(
            top: 60,
            left: 16,
            right: 16,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Left button shimmer
                Container(
                  height: 36,
                  width: 36,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(18),
                  ),
                ),
                // Right button shimmer
                Container(
                  height: 36,
                  width: 36,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(18),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}
